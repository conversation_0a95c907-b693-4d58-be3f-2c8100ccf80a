'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { WelcomeStep } from './_components/welcome-step';
import { WorkTypeStep } from './_components/work-type-step';
import { useCreateAgent, useAgents } from '@/hooks/react-query/agents/use-agents';
import { toast } from 'sonner';

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const router = useRouter();
  const createAgentMutation = useCreateAgent();

  // Fetch user's existing agents to check if they already have any
  const { data: agentsResponse, isLoading: isLoadingAgents } = useAgents({
    page: 1,
    limit: 1,
    sort_by: 'created_at',
    sort_order: 'desc'
  });

  const handleNextStep = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleComplete = async (workType: string) => {
    try {
      // Wait for agents data to load if it's still loading
      if (isLoadingAgents) {
        toast.info('Loading your agents...');
        return;
      }

      // Check if user already has agents
      const existingAgents = agentsResponse?.agents || [];

      if (existingAgents.length > 0) {
        // User already has agents, redirect to their most recent agent's builder page
        const mostRecentAgent = existingAgents[0]; // Already sorted by created_at desc
        toast.success('Welcome back! Let\'s continue setting up your agent.');
        router.push(`/agents/new/${mostRecentAgent.agent_id}?onboarding=true`);
        return;
      }

      // User has no agents, create a new one
      const newAgent = await createAgentMutation.mutateAsync({
        name: `My ${workType} Agent`,
        description: `An AI agent specialized for ${workType.toLowerCase()} tasks`,
        system_prompt: `You are an AI agent specialized in ${workType.toLowerCase()}. Help users with tasks related to ${workType.toLowerCase()}.`,
        configured_mcps: [],
        custom_mcps: [],
        agentpress_tools: {},
        is_default: false,
      });

      toast.success('Your first agent has been created!');

      // Navigate to the agent builder with the new agent
      // Add a query parameter to trigger the custom service popup
      router.push(`/agents/new/${newAgent.agent_id}?onboarding=true`);
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast.error('Failed to create your first agent. Please try again.');

      // Fallback: navigate to agents page
      router.push('/agents');
    }
  };

  return (
    <>
      {currentStep === 1 && (
        <WelcomeStep onNext={handleNextStep} />
      )}
      {currentStep === 2 && (
        <WorkTypeStep onNext={handleComplete} />
      )}
    </>
  );
}
