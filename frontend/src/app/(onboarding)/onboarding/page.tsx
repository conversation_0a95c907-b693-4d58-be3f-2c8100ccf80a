'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { WelcomeStep } from './_components/welcome-step';
import { WorkTypeStep } from './_components/work-type-step';
import { useCreateAgent } from '@/hooks/react-query/agents/use-agents';
import { toast } from 'sonner';

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const router = useRouter();
  const createAgentMutation = useCreateAgent();

  const handleNextStep = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleComplete = async (workType: string) => {
    try {
      // Create a new agent based on the work type
      const newAgent = await createAgentMutation.mutateAsync({
        name: `My ${workType} Agent`,
        description: `An AI agent specialized for ${workType.toLowerCase()} tasks`,
        instructions: `You are an AI agent specialized in ${workType.toLowerCase()}. Help users with tasks related to ${workType.toLowerCase()}.`,
        model: 'claude-3-5-sonnet-20241022',
        temperature: 0.7,
        max_tokens: 4000,
        tools: [],
        files: [],
      });

      toast.success('Your first agent has been created!');

      // Navigate to the agent builder with the new agent
      // Add a query parameter to trigger the custom service popup
      router.push(`/agents/new/${newAgent.agent_id}?onboarding=true`);
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast.error('Failed to create your first agent. Please try again.');
      
      // Fallback: navigate to agents page
      router.push('/agents');
    }
  };

  return (
    <>
      {currentStep === 1 && (
        <WelcomeStep onNext={handleNextStep} />
      )}
      {currentStep === 2 && (
        <WorkTypeStep onNext={handleComplete} />
      )}
    </>
  );
}
